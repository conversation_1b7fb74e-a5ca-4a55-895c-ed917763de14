# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
.next/
out/
dist/
build/

# Cache directories
.cache/
.parcel-cache/
.turbo/

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
temp/
tmp/

# Documentation
docs/
*.md
!README.md

# Test files
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Development scripts
scripts/deploy.sh
netlify.toml
