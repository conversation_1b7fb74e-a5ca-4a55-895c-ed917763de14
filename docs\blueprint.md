# **App Name**: Synthara AI Platform UI

## Core Features:

- Homepage Design: Modern, clean homepage for Synthara AI Platform.
- User Dashboard: Responsive user dashboard with left-side navigation.
- Data Generation: Data generation page with natural language prompt and AI suggestion panel. An LLM tool makes a recommendation regarding which pre-trained model might work best given the prompt.
- Data Preview: Preview/download interface for generated data with table view and statistics.
- ML Training: Machine learning training interface with model algorithm selector.
- User Profile: Profile and settings page with editable fields and API key generation.
- Help Center: Help and support center with FAQ and contact form.

## Style Guidelines:

- Primary color: Deep teal (#008080) to represent reliability, security, and cutting-edge data practices. We will use teal shades to convey precision, intelligence, and quality.
- Background color: Light teal (#E0F8F8), which is the same hue as the primary color but heavily desaturated. This ensures a gentle, unobtrusive background that keeps the focus on the content and provides a sophisticated base for the vibrant primary and accent colors.
- Accent color: Sky blue (#87CEEB). By being a related but lighter and brighter color on the spectrum, it suggests innovation and draws the eye. This accent color emphasizes AI suggestions.
- Font pairing: 'Space Grotesk' (sans-serif) for headers and titles, 'Inter' (sans-serif) for body text, giving a balance of technological aesthetic with clean, readable body text.
- Consistent icon set across all navigation and features for quick recognition.
- Clean and structured layouts, emphasizing usability and quick access to tools.
- Subtle animations for loading states and transitions to enhance user experience without distraction.