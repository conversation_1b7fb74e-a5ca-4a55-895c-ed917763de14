{"name": "synthara-ai", "version": "1.0.0", "private": true, "description": "AI-Powered Web Scraping Dataset Generator with Anthropic Claude Integration", "keywords": ["ai", "dataset", "web-scraping", "anthropic", "claude", "machine-learning"], "author": "Synthara Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "build:analyze": "ANALYZE=true npm run build", "build:netlify": "npm run clean && npm run build", "deploy:prepare": "bash scripts/deploy.sh", "postbuild": "echo 'Build completed successfully'", "clean": "rimraf .next out node_modules/.cache temp", "optimize": "npm run clean && npm ci && npm run build"}, "dependencies": {"@anthropic-ai/sdk": "^0.55.0", "@genkit-ai/googleai": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@mendable/firecrawl-js": "^1.27.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.5.0", "@supabase/supabase-js": "^2.45.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^17.0.0", "genkit": "^1.8.0", "lucide-react": "^0.475.0", "next": "15.3.3", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "serpapi": "^2.1.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.11.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}